# pip install requests
import requests, json, datetime, time, os
from concurrent.futures import ThreadPoolExecutor, as_completed

def clear_screen():
    """清屏函数"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_all_stocks():
    """获取所有A股股票列表（分页获取）"""
    print("正在获取股票列表...")

    url = "https://push2.eastmoney.com/api/qt/clist/get"
    all_stocks = []

    # 基础参数
    base_params = {
        "pz": 100,  # 每页100条（API限制）
        "po": 1,
        "np": 1,
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": 2,
        "invt": 2,
        "fid": "f62",
        # 完整的A股参数：包括沪深主板、中小板、创业板、科创板、北交所等
        "fs": "m:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23,m:0 t:81 s:2048",
        "fields": "f12,f14"  # 只获取代码和名称
    }

    page = 1
    max_pages = 60  # 最多获取60页，应该能覆盖所有股票

    while page <= max_pages:
        try:
            print(f"正在获取第 {page} 页数据...")

            # 设置当前页码
            params = base_params.copy()
            params["pn"] = page

            response = requests.get(url, params=params, timeout=10)

            if response.status_code != 200:
                print(f"第 {page} 页请求失败，状态码: {response.status_code}")
                break

            data = response.json()

            if not data.get("data") or not data["data"].get("diff"):
                print(f"第 {page} 页没有数据，停止获取")
                break

            stocks_data = data["data"]["diff"]
            page_count = len(stocks_data)
            print(f"第 {page} 页获取到 {page_count} 条数据")

            if page_count == 0:
                print("当前页无数据，停止获取")
                break

            # 处理当前页数据
            for stock in stocks_data:
                code = stock.get("f12", "")
                name = stock.get("f14", "")
                if code and name and len(code) == 6:  # 确保是6位股票代码
                    # 根据代码判断市场
                    if code.startswith(('60', '68', '90')):  # 沪市
                        secid = f"1.{code}"
                        market = "SH"
                    elif code.startswith(('00', '30', '20')):  # 深市
                        secid = f"0.{code}"
                        market = "SZ"
                    elif code.startswith(('8', '4')):  # 北交所
                        secid = f"0.{code}"
                        market = "BJ"
                    else:
                        continue  # 跳过不认识的代码

                    all_stocks.append({
                        "code": code,
                        "name": name,
                        "secid": secid,
                        "market": market
                    })

            # 如果当前页数据少于100条，说明已经是最后一页
            if page_count < 100:
                print(f"第 {page} 页数据不足100条，已获取完所有数据")
                break

            page += 1
            time.sleep(0.1)  # 稍微延迟避免请求过快

        except Exception as e:
            print(f"获取第 {page} 页数据失败: {e}")
            break

    # 去除重复股票（基于股票代码）
    print(f"去重前: {len(all_stocks)} 只股票")
    unique_stocks = {}
    for stock in all_stocks:
        code = stock["code"]
        if code not in unique_stocks:
            unique_stocks[code] = stock

    all_stocks = list(unique_stocks.values())
    print(f"去重后: {len(all_stocks)} 只股票")
    print(f"共获取 {page-1} 页数据，最终获取到 {len(all_stocks)} 只有效股票")
    return all_stocks

def get_stock_realtime_data(stock_info):
    """获取单只股票的实时数据"""
    try:
        secid = stock_info["secid"]
        code = stock_info["code"]
        name = stock_info["name"]
        market = stock_info["market"]

        # 实时行情
        url_rt = "https://push2.eastmoney.com/api/qt/stock/get"
        params_rt = {
            "secid": secid,
            "fields": "f43,f44,f45,f46,f47,f48,f60,f116,f117,f50,f51,f52,f53,f54,f55,f56,f57,f58,f107,f108,f111,f168,f169,f170,f152",
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
        }

        # 减少超时时间，提高并发效率
        response = requests.get(url_rt, params=params_rt, timeout=2)

        if response.status_code != 200:
            return None

        data = response.json()
        rt_data = data.get("data")

        if not rt_data or not rt_data.get("f43"):
            return None

        # 安全地转换数据
        try:
            price = round(float(rt_data["f43"]) / 100, 2) if rt_data.get("f43") else 0
            chg = round(float(rt_data["f44"]) / 100, 2) if rt_data.get("f44") else 0
            pct = round(float(rt_data["f170"]) / 100, 2) if rt_data.get("f170") else 0
            volume = int(rt_data["f47"]) if rt_data.get("f47") else 0
            turnover = round(float(rt_data["f48"]) / 1e8, 2) if rt_data.get("f48") else 0
        except (ValueError, TypeError):
            return None

        # 过滤无效数据
        if price <= 0:
            return None

        return {
            "code": code,
            "name": name,
            "market": market,
            "price": price,
            "chg": chg,
            "pct": pct,
            "volume": volume,
            "turnover": turnover
        }

    except requests.exceptions.Timeout:
        return None
    except requests.exceptions.RequestException:
        return None
    except Exception:
        return None

def display_stocks_data(stocks_data):
    """显示股票数据"""
    clear_screen()

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    valid_stocks = [s for s in stocks_data if s is not None]

    # 统计数据
    up_count = len([s for s in valid_stocks if s["pct"] > 0])
    down_count = len([s for s in valid_stocks if s["pct"] < 0])
    flat_count = len([s for s in valid_stocks if s["pct"] == 0])

    print(f"{'='*100}")
    print(f"实时股票行情数据 - 更新时间: {current_time}")
    print(f"总计: {len(valid_stocks)} 只 | 上涨: {up_count} 只 📈 | 下跌: {down_count} 只 📉 | 平盘: {flat_count} 只 ➖")
    print(f"{'='*100}")
    print(f"{'代码':<8} {'名称':<12} {'市场':<4} {'最新价':<8} {'涨跌额':<8} {'涨跌幅':<8} {'成交量(手)':<12} {'成交额(亿)':<10}")
    print(f"{'-'*100}")

    # 按涨跌幅排序
    sorted_stocks = sorted(valid_stocks, key=lambda x: x["pct"], reverse=True)

    # 显示涨幅榜前25名
    print("📈 涨幅榜 TOP 25:")
    for i, stock in enumerate(sorted_stocks[:25], 1):
        color = "📈" if stock["pct"] > 0 else "➖"
        print(f"{i:>2}. {stock['code']:<8} {stock['name']:<12} {stock['market']:<4} {stock['price']:<8.2f} "
              f"{stock['chg']:>+7.2f} {color}{stock['pct']:>+6.2f}% "
              f"{stock['volume']:<12,} {stock['turnover']:<10.2f}")

    print(f"{'-'*100}")

    # 显示跌幅榜前25名
    print("📉 跌幅榜 TOP 25:")
    bottom_stocks = sorted(valid_stocks, key=lambda x: x["pct"])[:25]
    for i, stock in enumerate(bottom_stocks, 1):
        color = "📉" if stock["pct"] < 0 else "➖"
        print(f"{i:>2}. {stock['code']:<8} {stock['name']:<12} {stock['market']:<4} {stock['price']:<8.2f} "
              f"{stock['chg']:>+7.2f} {color}{stock['pct']:>+6.2f}% "
              f"{stock['volume']:<12,} {stock['turnover']:<10.2f}")

    print(f"{'-'*100}")
    print("按 Ctrl+C 退出程序")

def get_stocks_batch(stocks_batch):
    """批量获取股票数据"""
    batch_results = []
    for stock in stocks_batch:
        result = get_stock_realtime_data(stock)
        if result:
            batch_results.append(result)
    return batch_results

def main():
    """主函数"""
    print("股票实时行情监控系统启动中...")

    # 获取所有股票列表
    all_stocks = get_all_stocks()

    if not all_stocks:
        print("未能获取到股票列表，程序退出")
        return

    print(f"开始监控 {len(all_stocks)} 只股票的实时行情...")
    print("程序将每30秒更新一次数据")
    time.sleep(2)

    try:
        while True:
            start_time = time.time()

            # 将股票分批处理，提高并发效率
            batch_size = 50  # 每批50只股票
            stock_batches = [all_stocks[i:i + batch_size] for i in range(0, len(all_stocks), batch_size)]

            print(f"分为 {len(stock_batches)} 批处理，每批 {batch_size} 只股票")

            stocks_data = []
            # 使用更多线程池并发获取数据
            with ThreadPoolExecutor(max_workers=50) as executor:
                # 提交批处理任务
                future_to_batch = {
                    executor.submit(get_stocks_batch, batch): batch
                    for batch in stock_batches
                }

                # 收集结果
                completed_batches = 0
                for future in as_completed(future_to_batch):
                    batch_results = future.result()
                    stocks_data.extend(batch_results)
                    completed_batches += 1

                    # 显示进度
                    progress = (completed_batches / len(stock_batches)) * 100
                    print(f"\r数据获取进度: {progress:.1f}% ({completed_batches}/{len(stock_batches)})", end="", flush=True)

            print()  # 换行

            # 显示数据
            display_stocks_data(stocks_data)

            # 计算耗时
            elapsed_time = time.time() - start_time
            print(f"数据获取耗时: {elapsed_time:.2f}秒，有效数据: {len(stocks_data)} 只")

            # 等待30秒后下次更新
            print("等待30秒后进行下次更新...")
            time.sleep(30)

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()