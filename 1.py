# pip install requests
import requests, json, datetime, time, os
from concurrent.futures import ThreadPoolExecutor, as_completed

def clear_screen():
    """清屏函数"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_all_stocks():
    """获取所有A股股票列表"""
    print("正在获取股票列表...")

    # 获取沪深A股列表
    url = "https://80.push2.eastmoney.com/api/qt/clist/get"

    all_stocks = []

    # 获取沪市A股 (f3=1表示沪市)
    params_sh = {
        "pn": 1,
        "pz": 5000,  # 每页数量
        "po": 1,
        "np": 1,
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": 2,
        "invt": 2,
        "fid": "f3",
        "fs": "m:1 t:2,m:1 t:23",  # 沪市A股
        "fields": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152"
    }

    try:
        response = requests.get(url, params=params_sh, timeout=10)
        data = response.json()
        if data.get("data") and data["data"].get("diff"):
            for stock in data["data"]["diff"]:
                code = stock.get("f12", "")
                name = stock.get("f14", "")
                if code and name:
                    all_stocks.append({
                        "code": code,
                        "name": name,
                        "secid": f"1.{code}",  # 沪市用1
                        "market": "SH"
                    })
    except Exception as e:
        print(f"获取沪市股票失败: {e}")

    # 获取深市A股 (f3=0表示深市)
    params_sz = {
        "pn": 1,
        "pz": 5000,
        "po": 1,
        "np": 1,
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": 2,
        "invt": 2,
        "fid": "f3",
        "fs": "m:0 t:6,m:0 t:80",  # 深市A股
        "fields": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152"
    }

    try:
        response = requests.get(url, params=params_sz, timeout=10)
        data = response.json()
        if data.get("data") and data["data"].get("diff"):
            for stock in data["data"]["diff"]:
                code = stock.get("f12", "")
                name = stock.get("f14", "")
                if code and name:
                    all_stocks.append({
                        "code": code,
                        "name": name,
                        "secid": f"0.{code}",  # 深市用0
                        "market": "SZ"
                    })
    except Exception as e:
        print(f"获取深市股票失败: {e}")

    print(f"共获取到 {len(all_stocks)} 只股票")
    return all_stocks

def get_stock_realtime_data(stock_info):
    """获取单只股票的实时数据"""
    try:
        secid = stock_info["secid"]
        code = stock_info["code"]
        name = stock_info["name"]
        market = stock_info["market"]

        # 实时行情
        url_rt = "https://push2.eastmoney.com/api/qt/stock/get"
        params_rt = {
            "secid": secid,
            "fields": "f43,f44,f45,f46,f47,f48,f60,f116,f117,f50,f51,f52,f53,f54,f55,f56,f57,f58,f107,f108,f111,f168,f169,f170,f152",
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
        }

        response = requests.get(url_rt, params=params_rt, timeout=3)
        rt_data = response.json().get("data")

        if not rt_data or not rt_data.get("f43"):
            return None

        price = round(float(rt_data["f43"]) / 100, 2) if rt_data.get("f43") else 0
        chg = round(float(rt_data["f44"]) / 100, 2) if rt_data.get("f44") else 0
        pct = round(float(rt_data["f170"]) / 100, 2) if rt_data.get("f170") else 0
        volume = int(rt_data["f47"]) if rt_data.get("f47") else 0
        turnover = round(float(rt_data["f48"]) / 1e8, 2) if rt_data.get("f48") else 0

        return {
            "code": code,
            "name": name,
            "market": market,
            "price": price,
            "chg": chg,
            "pct": pct,
            "volume": volume,
            "turnover": turnover
        }

    except Exception as e:
        return None

def display_stocks_data(stocks_data):
    """显示股票数据"""
    clear_screen()

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{'='*80}")
    print(f"实时股票行情数据 - 更新时间: {current_time}")
    print(f"{'='*80}")
    print(f"{'代码':<8} {'名称':<12} {'最新价':<8} {'涨跌额':<8} {'涨跌幅':<8} {'成交量(手)':<12} {'成交额(亿)':<10}")
    print(f"{'-'*80}")

    # 按涨跌幅排序
    valid_stocks = [s for s in stocks_data if s is not None]
    sorted_stocks = sorted(valid_stocks, key=lambda x: x["pct"], reverse=True)

    for i, stock in enumerate(sorted_stocks[:50]):  # 只显示前50只
        color = ""
        if stock["pct"] > 0:
            color = "📈"  # 上涨
        elif stock["pct"] < 0:
            color = "📉"  # 下跌
        else:
            color = "➖"  # 平盘

        print(f"{stock['code']:<8} {stock['name']:<12} {stock['price']:<8.2f} "
              f"{stock['chg']:>+7.2f} {color}{stock['pct']:>+6.2f}% "
              f"{stock['volume']:<12,} {stock['turnover']:<10.2f}")

    print(f"{'-'*80}")
    print(f"共显示前50只股票，总计 {len(valid_stocks)} 只有效数据")
    print("按 Ctrl+C 退出程序")

def main():
    """主函数"""
    print("股票实时行情监控系统启动中...")

    # 获取所有股票列表
    all_stocks = get_all_stocks()

    if not all_stocks:
        print("未能获取到股票列表，程序退出")
        return

    print(f"开始监控 {len(all_stocks)} 只股票的实时行情...")
    print("程序将每30秒更新一次数据")
    time.sleep(2)

    try:
        while True:
            start_time = time.time()

            # 使用线程池并发获取数据
            stocks_data = []
            with ThreadPoolExecutor(max_workers=20) as executor:
                # 提交所有任务
                future_to_stock = {
                    executor.submit(get_stock_realtime_data, stock): stock
                    for stock in all_stocks
                }

                # 收集结果
                for future in as_completed(future_to_stock):
                    result = future.result()
                    if result:
                        stocks_data.append(result)

            # 显示数据
            display_stocks_data(stocks_data)

            # 计算耗时
            elapsed_time = time.time() - start_time
            print(f"数据获取耗时: {elapsed_time:.2f}秒")

            # 等待30秒后下次更新
            time.sleep(30)

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()