# pip install requests aiohttp
import requests, json, datetime, time, os, asyncio, aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed

# 创建全局session复用连接
session = requests.Session()
session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
})

def clear_screen():
    """清屏函数"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_all_stocks():
    """获取所有A股股票列表（分页获取）"""
    print("正在获取股票列表...")

    url = "https://push2.eastmoney.com/api/qt/clist/get"
    all_stocks = []

    # 基础参数
    base_params = {
        "pz": 100,  # 每页100条（API限制）
        "po": 1,
        "np": 1,
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": 2,
        "invt": 2,
        "fid": "f62",
        # 完整的A股参数：包括沪深主板、中小板、创业板、科创板、北交所等
        "fs": "m:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23,m:0 t:81 s:2048",
        "fields": "f12,f14"  # 只获取代码和名称
    }

    page = 1
    max_pages = 60  # 最多获取60页，应该能覆盖所有股票

    while page <= max_pages:
        try:
            print(f"正在获取第 {page} 页数据...")

            # 设置当前页码
            params = base_params.copy()
            params["pn"] = page

            response = session.get(url, params=params, timeout=10)

            if response.status_code != 200:
                print(f"第 {page} 页请求失败，状态码: {response.status_code}")
                break

            data = response.json()

            if not data.get("data") or not data["data"].get("diff"):
                print(f"第 {page} 页没有数据，停止获取")
                break

            stocks_data = data["data"]["diff"]
            page_count = len(stocks_data)
            print(f"第 {page} 页获取到 {page_count} 条数据")

            if page_count == 0:
                print("当前页无数据，停止获取")
                break

            # 处理当前页数据
            for stock in stocks_data:
                code = stock.get("f12", "")
                name = stock.get("f14", "")
                if code and name and len(code) == 6:  # 确保是6位股票代码
                    # 根据代码判断市场
                    if code.startswith(('60', '68', '90')):  # 沪市
                        secid = f"1.{code}"
                        market = "SH"
                    elif code.startswith(('00', '30', '20')):  # 深市
                        secid = f"0.{code}"
                        market = "SZ"
                    elif code.startswith(('8', '4')):  # 北交所
                        secid = f"0.{code}"
                        market = "BJ"
                    else:
                        continue  # 跳过不认识的代码

                    all_stocks.append({
                        "code": code,
                        "name": name,
                        "secid": secid,
                        "market": market
                    })

            # 如果当前页数据少于100条，说明已经是最后一页
            if page_count < 100:
                print(f"第 {page} 页数据不足100条，已获取完所有数据")
                break

            page += 1
            time.sleep(0.1)  # 稍微延迟避免请求过快

        except Exception as e:
            print(f"获取第 {page} 页数据失败: {e}")
            break

    # 去除重复股票（基于股票代码）
    print(f"去重前: {len(all_stocks)} 只股票")
    unique_stocks = {}
    for stock in all_stocks:
        code = stock["code"]
        if code not in unique_stocks:
            unique_stocks[code] = stock

    all_stocks = list(unique_stocks.values())
    print(f"去重后: {len(all_stocks)} 只股票")
    print(f"共获取 {page-1} 页数据，最终获取到 {len(all_stocks)} 只有效股票")
    return all_stocks

async def get_stock_realtime_data_async(session, stock_info):
    """异步获取单只股票的实时数据 - 超级极速版"""
    try:
        secid = stock_info["secid"]
        code = stock_info["code"]
        name = stock_info["name"]
        market = stock_info["market"]

        url = "https://push2.eastmoney.com/api/qt/stock/get"
        params = {
            "secid": secid,
            "fields": "f43,f44,f170",  # 只获取核心3个字段，最大化速度
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
        }

        # 极短超时，最大化速度
        async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=1.0)) as response:
            if response.status != 200:
                return None

            data = await response.json()
            rt_data = data.get("data")

            if not rt_data or not rt_data.get("f43"):
                return None

            f43 = rt_data.get("f43")
            if not f43:
                return None

            price = round(float(f43) / 100, 2)
            if price <= 0:
                return None

            f44 = rt_data.get("f44")
            f170 = rt_data.get("f170")
            f47 = rt_data.get("f47")
            f48 = rt_data.get("f48")

            chg = round(float(f44) / 100, 2) if f44 else 0
            pct = round(float(f170) / 100, 2) if f170 else 0
            volume = int(f47) if f47 else 0
            turnover = round(float(f48) / 1e8, 2) if f48 else 0

            return {
                "code": code,
                "name": name,
                "market": market,
                "price": price,
                "chg": chg,
                "pct": pct,
                "volume": volume,
                "turnover": turnover
            }
    except asyncio.TimeoutError:
        return None  # 超时直接返回None，不打印错误
    except aiohttp.ClientError:
        return None  # 网络错误直接返回None
    except Exception as e:
        # 只打印前5个非网络错误，用于调试
        if not hasattr(get_stock_realtime_data_async, 'error_count'):
            get_stock_realtime_data_async.error_count = 0

        get_stock_realtime_data_async.error_count += 1
        if get_stock_realtime_data_async.error_count <= 5:
            print(f"数据解析错误 {code}: {type(e).__name__}")

        return None

async def get_all_stocks_data_async(all_stocks):
    """异步获取所有股票数据"""
    # 极限并发配置
    connector = aiohttp.TCPConnector(
        limit=1000,  # 最大连接池
        limit_per_host=500,  # 最大每主机连接数
        ttl_dns_cache=300,
        use_dns_cache=True,
        enable_cleanup_closed=True,
    )

    timeout = aiohttp.ClientTimeout(total=8)  # 8秒总超时

    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
    ) as session:

        print(f"开始异步请求 {len(all_stocks)} 只股票...")

        # 极限批次配置 - 最大化并发
        batch_size = 1500  # 更大批次
        all_results = []

        for i in range(0, len(all_stocks), batch_size):
            batch = all_stocks[i:i + batch_size]
            batch_num = i//batch_size + 1
            total_batches = (len(all_stocks) + batch_size - 1) // batch_size
            print(f"🚀 批次 {batch_num}/{total_batches}: {len(batch)} 只")

            # 创建当前批次的任务
            tasks = [get_stock_realtime_data_async(session, stock) for stock in batch]

            # 并发执行当前批次
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 极速过滤 - 减少循环开销
            valid_results = [r for r in batch_results if r is not None and not isinstance(r, Exception)]
            all_results.extend(valid_results)

            print(f"✅ {batch_num}: {len(valid_results)}/{len(batch)}")

            # 完全去掉延迟，最大化速度

        print(f"🎯 获得 {len(all_results)} 条数据")
        return all_results

def get_stocks_data_sync(all_stocks):
    """同步包装器"""
    return asyncio.run(get_all_stocks_data_async(all_stocks))

def display_stocks_data(stocks_data):
    """显示股票数据 - 超级极速版"""
    clear_screen()

    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    valid_count = len(stocks_data)

    # 超快统计
    up_count = sum(1 for s in stocks_data if s.get("pct", 0) > 0)
    down_count = valid_count - up_count - sum(1 for s in stocks_data if s.get("pct", 0) == 0)

    print(f"🚀 {current_time} | 总计:{valid_count} 📈{up_count} 📉{down_count}")
    print("="*60)

    # 只显示前15名，减少排序时间
    if valid_count > 0:
        # 使用部分排序，更快
        top_gainers = sorted(stocks_data, key=lambda x: x.get("pct", 0), reverse=True)[:15]
        top_losers = sorted(stocks_data, key=lambda x: x.get("pct", 0))[:15]

        print("📈 涨幅榜 TOP 15:")
        for i, s in enumerate(top_gainers, 1):
            turnover = s.get('turnover', 0)
            print(f"{i:>2}. {s['code']} {s['name']:<8} {s['price']:>6.2f} {s['pct']:>+5.2f}% 💰{turnover:>5.2f}亿")

        print("-"*60)
        print("📉 跌幅榜 TOP 15:")
        for i, s in enumerate(top_losers, 1):
            turnover = s.get('turnover', 0)
            print(f"{i:>2}. {s['code']} {s['name']:<8} {s['price']:>6.2f} {s['pct']:>+5.2f}% 💰{turnover:>5.2f}亿")

    print("="*60)
    print("Ctrl+C 退出")

# 删除旧的批处理函数，直接使用异步方法

def main():
    """主函数"""
    print("股票实时行情监控系统启动中...")

    # 获取所有股票列表
    all_stocks = get_all_stocks()

    if not all_stocks:
        print("未能获取到股票列表，程序退出")
        return

    print(f"开始监控 {len(all_stocks)} 只股票的实时行情...")
    print("程序将每30秒更新一次数据")
    time.sleep(2)

    try:
        while True:
            start_time = time.time()

            print(f"🚀 超级极速模式：异步并发获取 {len(all_stocks)} 只股票，目标5秒内完成！")

            # 使用异步方法获取所有数据
            stocks_data = get_stocks_data_sync(all_stocks)

            # 显示数据
            display_stocks_data(stocks_data)

            # 计算耗时
            elapsed_time = time.time() - start_time
            print(f"⚡ 数据获取耗时: {elapsed_time:.2f}秒，有效数据: {len(stocks_data)} 只")

            if elapsed_time <= 10:
                print("🎉 成功在10秒内完成！")
            else:
                print("⚠️  超过10秒，需要进一步优化")

            # 等待30秒后下次更新
            print("等待30秒后进行下次更新...")
            time.sleep(30)

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()