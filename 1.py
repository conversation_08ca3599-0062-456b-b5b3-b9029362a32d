# pip install requests aiohttp
import requests, json, datetime, time, os, asyncio, aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed

# 创建全局session复用连接
session = requests.Session()
session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
})

def clear_screen():
    """清屏函数"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_all_stocks():
    """获取所有A股股票列表（分页获取）"""
    print("正在获取股票列表...")

    url = "https://push2.eastmoney.com/api/qt/clist/get"
    all_stocks = []

    # 基础参数
    base_params = {
        "pz": 100,  # 每页100条（API限制）
        "po": 1,
        "np": 1,
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": 2,
        "invt": 2,
        "fid": "f62",
        # 完整的A股参数：包括沪深主板、中小板、创业板、科创板、北交所等
        "fs": "m:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23,m:0 t:81 s:2048",
        "fields": "f12,f14"  # 只获取代码和名称
    }

    page = 1
    max_pages = 60  # 最多获取60页，应该能覆盖所有股票

    while page <= max_pages:
        try:
            print(f"正在获取第 {page} 页数据...")

            # 设置当前页码
            params = base_params.copy()
            params["pn"] = page

            response = session.get(url, params=params, timeout=10)

            if response.status_code != 200:
                print(f"第 {page} 页请求失败，状态码: {response.status_code}")
                break

            data = response.json()

            if not data.get("data") or not data["data"].get("diff"):
                print(f"第 {page} 页没有数据，停止获取")
                break

            stocks_data = data["data"]["diff"]
            page_count = len(stocks_data)
            print(f"第 {page} 页获取到 {page_count} 条数据")

            if page_count == 0:
                print("当前页无数据，停止获取")
                break

            # 处理当前页数据
            for stock in stocks_data:
                code = stock.get("f12", "")
                name = stock.get("f14", "")
                if code and name and len(code) == 6:  # 确保是6位股票代码
                    # 根据代码判断市场
                    if code.startswith(('60', '68', '90')):  # 沪市
                        secid = f"1.{code}"
                        market = "SH"
                    elif code.startswith(('00', '30', '20')):  # 深市
                        secid = f"0.{code}"
                        market = "SZ"
                    elif code.startswith(('8', '4')):  # 北交所
                        secid = f"0.{code}"
                        market = "BJ"
                    else:
                        continue  # 跳过不认识的代码

                    all_stocks.append({
                        "code": code,
                        "name": name,
                        "secid": secid,
                        "market": market
                    })

            # 如果当前页数据少于100条，说明已经是最后一页
            if page_count < 100:
                print(f"第 {page} 页数据不足100条，已获取完所有数据")
                break

            page += 1
            time.sleep(0.1)  # 稍微延迟避免请求过快

        except Exception as e:
            print(f"获取第 {page} 页数据失败: {e}")
            break

    # 去除重复股票（基于股票代码）
    print(f"去重前: {len(all_stocks)} 只股票")
    unique_stocks = {}
    for stock in all_stocks:
        code = stock["code"]
        if code not in unique_stocks:
            unique_stocks[code] = stock

    all_stocks = list(unique_stocks.values())
    print(f"去重后: {len(all_stocks)} 只股票")
    print(f"共获取 {page-1} 页数据，最终获取到 {len(all_stocks)} 只有效股票")
    return all_stocks

async def get_stock_realtime_data_async(session, stock_info):
    """异步获取单只股票的实时数据 - 超级极速版"""
    try:
        secid = stock_info["secid"]
        code = stock_info["code"]
        name = stock_info["name"]
        market = stock_info["market"]

        url = "https://push2.eastmoney.com/api/qt/stock/get"
        params = {
            "secid": secid,
            "fields": "f43,f44,f170,f47,f48",  # 恢复更多字段
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
        }

        # 减少超时时间提高速度
        async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=1)) as response:
            if response.status != 200:
                return None

            data = await response.json()
            rt_data = data.get("data")

            if not rt_data or not rt_data.get("f43"):
                return None

            f43 = rt_data.get("f43")
            if not f43:
                return None

            price = round(float(f43) / 100, 2)
            if price <= 0:
                return None

            f44 = rt_data.get("f44")
            f170 = rt_data.get("f170")
            f47 = rt_data.get("f47")
            f48 = rt_data.get("f48")

            chg = round(float(f44) / 100, 2) if f44 else 0
            pct = round(float(f170) / 100, 2) if f170 else 0
            volume = int(f47) if f47 else 0
            turnover = round(float(f48) / 1e8, 2) if f48 else 0

            return {
                "code": code,
                "name": name,
                "market": market,
                "price": price,
                "chg": chg,
                "pct": pct,
                "volume": volume,
                "turnover": turnover
            }
    except Exception as e:
        # 调试用：打印前几个错误
        if hasattr(get_stock_realtime_data_async, 'error_count'):
            get_stock_realtime_data_async.error_count += 1
        else:
            get_stock_realtime_data_async.error_count = 1

        if get_stock_realtime_data_async.error_count <= 3:
            print(f"请求错误 {code}: {e}")

        return None

async def get_all_stocks_data_async(all_stocks):
    """异步获取所有股票数据"""
    # 激进并发配置
    connector = aiohttp.TCPConnector(
        limit=800,  # 增加总连接池大小
        limit_per_host=400,  # 增加每个主机的连接数
        ttl_dns_cache=300,
        use_dns_cache=True,
    )

    timeout = aiohttp.ClientTimeout(total=10)  # 减少总超时时间

    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
    ) as session:

        print(f"开始异步请求 {len(all_stocks)} 只股票...")

        # 更大批次，减少批次数量
        batch_size = 1000
        all_results = []

        for i in range(0, len(all_stocks), batch_size):
            batch = all_stocks[i:i + batch_size]
            batch_num = i//batch_size + 1
            total_batches = (len(all_stocks) + batch_size - 1) // batch_size
            print(f"⚡ 批次 {batch_num}/{total_batches}: {len(batch)} 只股票")

            # 创建当前批次的任务
            tasks = [get_stock_realtime_data_async(session, stock) for stock in batch]

            # 并发执行当前批次
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 快速过滤有效结果
            valid_count = 0
            for r in batch_results:
                if r is not None and not isinstance(r, Exception):
                    all_results.append(r)
                    valid_count += 1

            print(f"✅ 批次 {batch_num} 完成: {valid_count} 条有效数据")

            # 去掉批次间延迟，最大化速度

        print(f"🎯 总计获得 {len(all_results)} 条有效数据")
        return all_results

def get_stocks_data_sync(all_stocks):
    """同步包装器"""
    return asyncio.run(get_all_stocks_data_async(all_stocks))

def display_stocks_data(stocks_data):
    """显示股票核心数据：实时股价、涨幅、成交量"""
    clear_screen()

    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    valid_count = len(stocks_data)

    # 统计
    up_count = sum(1 for s in stocks_data if s.get("pct", 0) > 0)
    down_count = sum(1 for s in stocks_data if s.get("pct", 0) < 0)
    flat_count = valid_count - up_count - down_count

    print(f"🚀 实时股票行情 {current_time}")
    print(f"总计:{valid_count} 📈上涨:{up_count} 📉下跌:{down_count} ➡️平盘:{flat_count}")
    print("="*90)

    if valid_count > 0:
        # 显示前15名涨幅榜
        top_gainers = sorted(stocks_data, key=lambda x: x.get("pct", 0), reverse=True)[:15]

        print("📈 涨幅榜 TOP 15 (实时股价+涨幅+成交量):")
        print(f"{'序号':<4} {'代码':<8} {'名称':<10} {'现价':<8} {'涨跌幅':<8} {'涨跌额':<8} {'成交量(万手)':<12} {'成交额(亿)':<10}")
        print("-"*90)

        for i, s in enumerate(top_gainers, 1):
            volume_wan = round(s.get('volume', 0) / 10000, 2)  # 转换为万手
            print(f"{i:<4} {s['code']:<8} {s['name']:<10} {s['price']:<8.2f} {s['pct']:<+8.2f}% {s['chg']:<+8.2f} {volume_wan:<12.2f} {s.get('turnover', 0):<10.2f}")

        print("\n📉 跌幅榜 TOP 15 (实时股价+涨幅+成交量):")
        print(f"{'序号':<4} {'代码':<8} {'名称':<10} {'现价':<8} {'涨跌幅':<8} {'涨跌额':<8} {'成交量(万手)':<12} {'成交额(亿)':<10}")
        print("-"*90)

        top_losers = sorted(stocks_data, key=lambda x: x.get("pct", 0))[:15]
        for i, s in enumerate(top_losers, 1):
            volume_wan = round(s.get('volume', 0) / 10000, 2)  # 转换为万手
            print(f"{i:<4} {s['code']:<8} {s['name']:<10} {s['price']:<8.2f} {s['pct']:<+8.2f}% {s['chg']:<+8.2f} {volume_wan:<12.2f} {s.get('turnover', 0):<10.2f}")

    print("="*90)
    print("💡 输入股票代码查看分时K线，或按 Ctrl+C 退出")

def get_stock_kline_sync(stock_code):
    """同步获取单只股票的分时K线数据"""
    try:
        # 构造secid
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"  # 上海
        elif stock_code.startswith('0') or stock_code.startswith('3'):
            secid = f"0.{stock_code}"  # 深圳
        elif stock_code.startswith('8') or stock_code.startswith('4'):
            secid = f"0.{stock_code}"  # 北京
        else:
            return None

        # 分时数据API
        url = "https://push2his.eastmoney.com/api/qt/stock/trends2/get"
        params = {
            "secid": secid,
            "fields1": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13",
            "fields2": "f51,f52,f53,f54,f55,f56,f57,f58",
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
            "iscr": "0",
            "ndays": "1"  # 获取1天的分时数据
        }

        response = session.get(url, params=params, timeout=5)
        if response.status_code != 200:
            return None

        data = response.json()
        trends_data = data.get("data", {}).get("trends", [])

        if not trends_data:
            return None

        # 解析分时数据
        kline_data = []
        for trend in trends_data:
            parts = trend.split(",")
            if len(parts) >= 4:
                time_str = parts[0]  # 时间 YYYY-MM-DD HH:MM
                price = round(float(parts[2]), 2)  # 价格
                volume = int(parts[3])   # 成交量

                # 只保留时间部分 HH:MM
                time_part = time_str.split(" ")[1] if " " in time_str else time_str

                kline_data.append({
                    "time": time_part,
                    "price": price,
                    "volume": volume
                })

        return kline_data
    except:
        return None

def display_stock_kline(stock_code):
    """显示股票分时K线数据"""
    print(f"\n🔍 正在获取 {stock_code} 的分时K线数据...")

    kline_data = get_stock_kline_sync(stock_code)

    if not kline_data:
        print(f"❌ 无法获取 {stock_code} 的分时数据")
        return

    print(f"\n📊 {stock_code} 分时K线数据 (最新{len(kline_data)}个数据点):")
    print(f"{'时间':<8} {'价格':<8} {'成交量':<12}")
    print("-"*30)

    # 显示最近30个分时点
    recent_data = kline_data[-30:] if len(kline_data) > 30 else kline_data

    for point in recent_data:
        volume_wan = round(point['volume'] / 10000, 2)  # 转换为万手
        print(f"{point['time']:<8} {point['price']:<8.2f} {volume_wan:<12.2f}万手")

    print("-"*30)
    print(f"数据更新时间: {datetime.datetime.now().strftime('%H:%M:%S')}")
    print("按回车键返回主界面...")

# 删除旧的批处理函数，直接使用异步方法

def main():
    """主函数"""
    print("股票实时行情监控系统启动中...")

    # 获取所有股票列表
    all_stocks = get_all_stocks()

    if not all_stocks:
        print("未能获取到股票列表，程序退出")
        return

    print(f"开始监控 {len(all_stocks)} 只股票的实时行情...")
    print("程序将每30秒更新一次数据")
    time.sleep(2)

    try:
        while True:
            start_time = time.time()

            print(f"🚀 超级极速模式：异步并发获取 {len(all_stocks)} 只股票，目标5秒内完成！")

            # 使用异步方法获取所有数据
            stocks_data = get_stocks_data_sync(all_stocks)

            # 显示数据
            display_stocks_data(stocks_data)

            # 计算耗时
            elapsed_time = time.time() - start_time
            print(f"⚡ 数据获取耗时: {elapsed_time:.2f}秒，有效数据: {len(stocks_data)} 只")

            if elapsed_time <= 10:
                print("🎉 成功在10秒内完成！")
            else:
                print("⚠️  超过10秒，需要进一步优化")

            # 等待30秒后下次更新
            print("等待30秒后进行下次更新...")
            print("💡 提示：程序运行中，如需查看分时K线，请先按 Ctrl+C 停止，然后重新运行程序")
            time.sleep(30)

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

def kline_query_mode():
    """分时K线查询模式"""
    print("📊 分时K线查询模式")
    print("="*50)

    while True:
        try:
            stock_code = input("\n请输入股票代码 (输入 'q' 退出, 'back' 返回主程序): ").strip()

            if stock_code.lower() == 'q':
                print("退出程序")
                break
            elif stock_code.lower() == 'back':
                print("返回主程序...")
                main()
                break
            elif stock_code:
                display_stock_kline(stock_code)
                input("\n按回车键继续...")
            else:
                print("请输入有效的股票代码")

        except KeyboardInterrupt:
            print("\n程序已停止")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    print("🚀 股票实时行情系统")
    print("1. 实时行情监控")
    print("2. 分时K线查询")

    try:
        choice = input("\n请选择模式 (1/2): ").strip()

        if choice == "1":
            main()
        elif choice == "2":
            kline_query_mode()
        else:
            print("默认启动实时行情监控...")
            main()

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序启动失败: {e}")