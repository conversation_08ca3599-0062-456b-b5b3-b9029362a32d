# pip install requests
import requests, json, datetime

# ========== 1. 准备参数 ==========
# 平安银行（000001.SZ）
secid = "0.000001"      # 0=sz，1=sh
today = datetime.datetime.now().strftime("%Y%m%d")

# ========== 2. 实时行情 ==========
url_rt = "https://push2.eastmoney.com/api/qt/stock/get"
params_rt = {
    "secid": secid,
    "fields": "f43,f44,f45,f46,f47,f48,f60,f116,f117,f50,f51,f52,f53,f54,f55,f56,f57,f58,f107,f108,f111,f168,f169,f170,f152",
    "ut": "fa5fd1943c7b386f172d6893dbfba10b",
}
rt = requests.get(url_rt, params=params_rt, timeout=5).json()["data"]

price   = round(float(rt["f43"]) / 100, 2)       # 最新价
chg     = round(float(rt["f44"]) / 100, 2)       # 涨跌额
pct     = round(float(rt["f170"]) / 100, 2)      # 涨幅 %
volume  = int(rt["f47"])                         # 当日成交量（手）
turnover= round(float(rt["f48"]) / 1e8, 2)       # 成交额（亿）

# ========== 3. 最新一根日 K 线 ==========
url_k = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
params_k = {
    "secid": secid,
    "klt": 101,          # 101=日 K
    "fqt": 1,
    "beg": today,
    "end": today,
    "fields1": "f1,f2,f3,f4,f5",
    "fields2": "f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61",
    "ut": "fa5fd1943c7b386f172d6893dbfba10b",
}
kline = requests.get(url_k, params=params_k, timeout=5).json()["data"]["klines"][0]
date, open_, high, low, close, *_, vol_hand, amount = kline.split(',')

# ========== 4. 结果打印 ==========
print("平安银行 000001.SZ")
print("最新价    :", price, "元")
print("涨跌额    :", chg, "元")
print("涨跌幅    :", pct, "%")
print("成交量    :", volume, "手")
print("成交额    :", turnover, "亿元")
print("最新日 K 线:")
print("  日期    :", date)
print("  开盘    :", open_, "元")
print("  最高    :", high, "元")
print("  最低    :", low, "元")
print("  收盘    :", close, "元")
print("  成交量  :", vol_hand, "手")
print("  成交额  :", amount, "元")